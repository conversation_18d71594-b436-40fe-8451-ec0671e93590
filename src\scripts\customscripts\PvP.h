#pragma once
#include "Common.h"
#include "Player.h"
#include <vector>
#include <string>
#include <unordered_map>

#define FACTION_DUEL 10000
#define GUILD_DUEL 20000

enum PvPTypes
{
	PVP_TYPE_NONE = 0,	//保持原状
	PVP_TYPE_SANCTUARY = 1,	//安全区
	PvP_TYPE_FFA = 2,	//FFAPVP
	PvP_TYPE_NO_GROUP_FFA = 3,	//禁止组队FFAPVP
	PvP_TYPE_CUSTOM_FACTION = 4,	//自定义阵营
	PvP_TYPE_GUILD = 5,//公会
};

struct PvPTemplate
{
	uint32 zone;
	uint32 area;
	PvPTypes type;
	std::string notice;
	uint32 minHp;
	uint32 maxHp;
	uint32 killRewId;
	uint32 killedRewId;
	bool autoMaxHp;
	uint32 IsPKlostGear;
	uint32 chaceDropGear;
	uint32 ImmueBuffId;
    std::vector<uint32> disabledAurasOnEnter;
};

struct ArenaTemplate
{
	uint32 playerguild;
	uint32 kills;
	uint32 wins;
	uint32 lose;
	std::string playername;
	uint32 IsRewarded;
};


extern std::vector<PvPTemplate> PvPVec;

class PvP
{
public:
	static PvP* instance()
	{
		static PvP instance;
		return &instance;
	}
	void Load();
	PvPTypes GetType(uint32 zone, uint32 area);
	bool EnableGroup(uint32 zone, uint32 area);
	void Update(Player* player, uint32 zone, uint32 area);
	void HookUpdateArea(Player* player, uint32 newArea);
	void SaveArenaPlayerKills(Player* player);
	void SaveArenaPlayerWinsOrLose(Player* player, bool Wins);
	std::vector<ArenaTemplate> GetTop10ByKills(std::unordered_map<uint32, ArenaTemplate>& arenaMap);
	void SavetoArenaDB();
	void InitialArenaDB(Player* player);
	void DaillyRewardResetArenaDB();
	uint32 GetMinHp(uint32 zone, uint32 area);
	uint32 GetMaxHp(uint32 zone, uint32 area);
	bool AutoMaxHP(uint32 zone, uint32 area);
	void HookOnPvpKillRew(Player *pKiller, Player *pVictim);

	std::unordered_map <uint32, ArenaTemplate> ArenaMap;
private:
};
#define sPvP PvP::instance()
