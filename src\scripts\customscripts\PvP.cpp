#pragma execution_character_set("utf-8")
#include "PvP.h"
#include "Reward.h"
#include "SpellAuras.h"

std::vector<PvPTemplate> PvPVec;

void PvP::Load()
{
    PvPVec.clear();
    auto result = WorldDatabase.PQuery("SELECT * FROM _属性调整_区域");
    if (!result)
    {
        sLog.outString("找不到 _属性调整_区域表！");
        return;
    }

    else if (result)
    {
        do
        {
            Field* fields = result->Fetch();
            PvPTemplate Temp;
            Temp.zone = fields[1].GetInt32();
            Temp.area = fields[2].GetUInt32();

            const char* type = fields[3].GetString();
            if (strcmp("安全区", type) == 0)
                Temp.type = PVP_TYPE_SANCTUARY;
            else if (strcmp("自由PVP", type) == 0)
                Temp.type = PvP_TYPE_FFA;
            else if (strcmp("自由PVP - 禁止组队", type) == 0)
                Temp.type = PvP_TYPE_NO_GROUP_FFA;
            else if (strcmp("自定义阵营", type) == 0)
                Temp.type = PvP_TYPE_CUSTOM_FACTION;
            else if (strcmp("公会PVP", type) == 0)
                Temp.type = PvP_TYPE_GUILD;
            else
                Temp.type = PVP_TYPE_NONE;

            Temp.minHp = fields[4].GetInt32();
            Temp.maxHp = fields[5].GetInt32();
            Temp.notice = fields[6].GetString();
            Temp.killRewId = fields[7].GetInt32();
            Temp.killedRewId = fields[8].GetInt32();
            Temp.autoMaxHp = fields[9].GetBool();

            Tokenizer PvpGearData(fields[10].GetString(), '#');
            Temp.IsPKlostGear = atoi(PvpGearData[0]);
            Temp.chaceDropGear = atoi(PvpGearData[1]);
            Temp.ImmueBuffId = atoi(PvpGearData[2]);

            // 读取第11个字段：进入区域失效光环
            Temp.disabledAurasOnEnter.clear();
            std::string disabledAuras = fields[11].GetString();
            if (!disabledAuras.empty())
            {
                Tokenizer auraTokens(disabledAuras, ' ');
                for (const auto& token : auraTokens)
                {
                    uint32 auraId = atoi(token);
                    if (auraId > 0)
                        Temp.disabledAurasOnEnter.push_back(auraId);
                }
            }

            PvPVec.push_back(Temp);
        }
        while (result->NextRow());
    }
    sLog.outString("<<加载 _属性调整_区域表 成功.");

    //result = CharacterDatabase.PQuery("SELECT * FROM _玩家竞技场数据");
    //if (result)
    //{
    //    CharacterDatabase.PExecute("DELETE FROM _玩家竞技场数据 WHERE _玩家竞技场数据.guid NOT IN(	SELECT guid FROM characters)");

    //    do
    //    {
    //        Field* fields = result->Fetch();
    //        ArenaTemplate Temp;

    //        Temp.playerguild = fields[0].GetInt32();
    //        Temp.kills = fields[1].GetInt32();
    //        Temp.wins = fields[2].GetInt32();
    //        Temp.lose = fields[3].GetInt32();
    //        Temp.playername = fields[4].GetString();
    //        Temp.IsRewarded = fields[5].GetInt32();
    //        ArenaMap.insert(std::make_pair(Temp.playerguild, Temp));
    //    }
    //    while (result->NextRow());
    //}
    //sLog.outString("<<加载 _玩家竞技场数据 成功.");

    delete result;
}

PvPTypes PvP::GetType(uint32 zone, uint32 area)
{
    uint32 length = PvPVec.size();

    for (size_t i = 0; i < length; i++)
        if (PvPVec[i].zone == zone && PvPVec[i].area == 0 || PvPVec[i].zone == zone && PvPVec[i].area == area)
            return PvPVec[i].type;

    return PVP_TYPE_NONE;
}

uint32 PvP::GetMinHp(uint32 zone, uint32 area)
{
    uint32 length = PvPVec.size();

    for (size_t i = 0; i < length; i++)
        if (PvPVec[i].zone == zone && PvPVec[i].area == 0 || PvPVec[i].zone == zone && PvPVec[i].area == area)
            return PvPVec[i].minHp;

    return 0;
}

uint32 PvP::GetMaxHp(uint32 zone, uint32 area)
{
    uint32 length = PvPVec.size();

    for (size_t i = 0; i < length; i++)
        if (PvPVec[i].zone == zone && PvPVec[i].area == 0 || PvPVec[i].zone == zone && PvPVec[i].area == area)
            return PvPVec[i].maxHp;

    return 0;
}

bool PvP::AutoMaxHP(uint32 zone, uint32 area)
{
    uint32 length = PvPVec.size();

    for (size_t i = 0; i < length; i++)
        if (PvPVec[i].zone == zone && PvPVec[i].area == 0 || PvPVec[i].zone == zone && PvPVec[i].area == area)
            return PvPVec[i].autoMaxHp;

    return false;
}

void PvP::HookOnPvpKillRew(Player* pKiller, Player* pVictim)
{
    uint32 zone = pKiller->GetZoneId();
    uint32 area = pKiller->GetAreaId();

    if (pVictim->GetMapId() == 37) // 积雪平原 吃鸡
        pVictim->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_SKINNABLE);

    for (auto itr = PvPVec.begin(); itr != PvPVec.end(); itr++)
    {
        if (itr->zone == zone && itr->area == 0 || itr->zone == zone && itr->area == area)
        {
            // bool valid = false;  //William Todo 假如事件判断

            // if (itr->eventData.empty())
            //	valid = true;

            // for (auto i = itr->eventData.begin(); i != itr->eventData.end(); i++)
            //	if (sGameEventMgr->IsActiveEvent(*i))
            //		valid = true;
            sRew->Rew(pKiller, itr->killRewId);
            sRew->Rew(pVictim, itr->killedRewId);

            // PVP指定地图爆装
            if (itr->IsPKlostGear && urand(1, 100) <= itr->chaceDropGear)
            {
                if (pVictim->HasAura(itr->ImmueBuffId)) // 免爆buff
                    return;

                std::vector<int> tempsavegear;
                tempsavegear.clear();

                for (int i = EQUIPMENT_SLOT_START; i < EQUIPMENT_SLOT_END; ++i) // 遍历身上装备,记录到临时容器中
                {
                    if (Item* pItem = pVictim->GetItemByPos(INVENTORY_SLOT_BAG_0, i))
                        tempsavegear.push_back(i);
                }

                if (!tempsavegear.empty()) // 容器不为空,则选择一个装备爆出,爆出装备继承附魔
                {
                    uint32 slot = SelectRandomContainerElement(tempsavegear);
                    if (Item* pItem = pVictim->GetItemByPos(INVENTORY_SLOT_BAG_0, slot))
                    {
                        Item* newitem = pKiller->AddItem(pItem->GetEntry(), 1);
                        if (newitem)
                        {
                            for (uint8 slot = PERM_ENCHANTMENT_SLOT; slot < MAX_ENCHANTMENT_SLOT; slot++)
                            {
                                uint32 enchantId = pItem->GetEnchantmentId(EnchantmentSlot(slot));
                                if (SpellItemEnchantmentEntry const* info = sSpellItemEnchantmentStore.LookupEntry(enchantId))
                                {
                                    pKiller->ApplyEnchantment(newitem, EnchantmentSlot(slot), false);
                                    newitem->SetEnchantment(EnchantmentSlot(slot), enchantId, 0, 0);
                                    pKiller->ApplyEnchantment(newitem, EnchantmentSlot(slot), true);
                                }
                            }
                        }
                        pVictim->DestroyItem(INVENTORY_SLOT_BAG_0, slot, true);
                    }
                }
            }
            return;
        }
    }
}

bool PvP::EnableGroup(uint32 zone, uint32 area)
{
    uint32 length = PvPVec.size();

    for (size_t i = 0; i < length; i++)
        if (PvPVec[i].zone == zone && PvPVec[i].area == 0 || PvPVec[i].zone == zone && PvPVec[i].area == area)
            if (PvPVec[i].type == PvP_TYPE_NO_GROUP_FFA)
                return false;

    return true;
}

void PvP::Update(Player* player, uint32 zone, uint32 area)
{
    bool automaxhp = AutoMaxHP(zone, area);

    player->UpdateMaxHealth();

    if (automaxhp)
        player->SetHealth(player->GetMaxHealth());

    if (Unit* pet = player->GetPet())
        pet->UpdateMaxHealth();

    // 处理光环失效/恢复逻辑
    static std::unordered_map<uint32, std::vector<uint32>> playerDisabledAuras; // [playerGUID] = [auraIDs]
    uint32 playerGUID = player->GetGUIDLow();

    uint32 length = PvPVec.size();
    for (size_t i = 0; i < length; i++)
    {
        if (PvPVec[i].zone == zone && PvPVec[i].area == 0 || PvPVec[i].zone == zone && PvPVec[i].area == area)
        {
            if (!PvPVec[i].notice.empty())
                player->SendChatMessage(PvPVec[i].notice.c_str());

            switch (PvPVec[i].type)
            {
            case PvP_TYPE_NO_GROUP_FFA:
                player->UninviteFromGroup();
                if (player->GetGroup())
                    player->RemoveFromGroup();
                break;
            }

            // 进入此区域，如果有失效光环配置，则禁用光环
            if (!PvPVec[i].disabledAurasOnEnter.empty())
            {
                playerDisabledAuras[playerGUID] = PvPVec[i].disabledAurasOnEnter; // 记录被禁用的光环列表

                for (uint32 auraId : PvPVec[i].disabledAurasOnEnter)
                {
                    SpellAuraHolder* auraHolder = player->GetSpellAuraHolder(auraId);
                    if (auraHolder)
                    {
                        Aura* aura = auraHolder->GetAuraByEffectIndex(EFFECT_INDEX_0);
                        if (aura)
                        {
                            Modifier* modifier = aura->GetModifier();
                            if (modifier && modifier->m_amount != 0)
                            {
                                // 禁用光环效果
                                aura->ApplyModifier(false, true);
                                modifier->m_amount = 0;
                                aura->ApplyModifier(true, true);
                            }
                        }
                    }
                }
            }
            return; // 找到匹配区域后直接返回
        }
    }

    // 没有找到匹配的区域，说明离开了配置区域，恢复光环
    auto playerIt = playerDisabledAuras.find(playerGUID);
    if (playerIt != playerDisabledAuras.end())
    {
        for (uint32 auraId : playerIt->second)
        {
            SpellAuraHolder* auraHolder = player->GetSpellAuraHolder(auraId);
            if (auraHolder)
            {
                Aura* aura = auraHolder->GetAuraByEffectIndex(EFFECT_INDEX_0);
                if (aura)
                {
                    Modifier* modifier = aura->GetModifier();
                    if (modifier && modifier->m_amount == 0)
                    {
                        // 从SpellProto读取原始值
                        SpellEntry const* spellProto = aura->GetSpellProto();
                        int32 originalValue = spellProto->CalculateSimpleValue(EFFECT_INDEX_0);

                        // 恢复光环效果
                        aura->ApplyModifier(false, true);
                        modifier->m_amount = originalValue;
                        aura->ApplyModifier(true, true);
                    }
                }
            }
        }
        // 清除该玩家的记录
        playerDisabledAuras.erase(playerIt);
    }
}

void PvP::HookUpdateArea(Player* player, uint32 newArea)
{
    PvPTypes pvpType = sPvP->GetType(player->GetZoneId(), newArea);

    if (pvpType == PVP_TYPE_SANCTUARY)
    {
        player->SetFactionTemplateId(35);

        if (newArea == 976 && !player->IsGameMaster())       //加基森无敌 
            player->SetInvincibilityHpThreshold(1);  // 血量降到1时无敌，实现真正的无敌效果

    }
    else
    {
        if (player->GetFactionTemplateId() == 35)
            player->RestoreFaction();

        if (player->GetInvincibilityHpThreshold())
            player->SetInvincibilityHpThreshold(0);
    }
}

void PvP::SaveArenaPlayerKills(Player* player) { ArenaMap[player->GetGUIDLow()].kills += 1; }

void PvP::SaveArenaPlayerWinsOrLose(Player* player, bool Wins)
{
    if (Wins)
        ArenaMap[player->GetGUIDLow()].wins += 1;
    else
        ArenaMap[player->GetGUIDLow()].lose += 1;
}

// 自定义比较函数,用于根据 kills 属性进行排序
bool compareByKills(const ArenaTemplate& a, const ArenaTemplate& b) { return a.kills > b.kills; }

// 获取 kills 前 10 的 ArenaTemplate 对象
std::vector<ArenaTemplate> PvP::GetTop10ByKills(std::unordered_map<uint32, ArenaTemplate>& arenaMap)
{
    // 将 arenaMap 中的所有值复制到一个 vector 中
    std::vector<ArenaTemplate> arenaVector;
    arenaVector.reserve(arenaMap.size());
    for (const auto& pair : arenaMap)
    {
        arenaVector.push_back(pair.second);
    }

    // 根据 kills 属性对 vector 进行排序
    std::sort(arenaVector.begin(), arenaVector.end(), compareByKills);

    // 返回前 10 个元素
    if (arenaVector.size() > 10)
    {
        return std::vector<ArenaTemplate>(arenaVector.begin(), arenaVector.begin() + 10);
    }
    else
    {
        return arenaVector;
    }
}

//void PvP::SavetoArenaDB()
//{
//    static SqlStatementID SavearenaDB;
//
//    CharacterDatabase.BeginTransaction();
//
//    SqlStatement stmt = CharacterDatabase.CreateStatement(SavearenaDB, "REPLACE INTO `_玩家竞技场数据` (`guid`, `kills`, `wins`, `lose`, `name`, `rewarded`) VALUES (?, ?, ?, ?, ?, ?)");
//
//    for (auto& x : ArenaMap)
//    {
//        stmt.addUInt32(x.second.playerguild);
//        stmt.addUInt32(x.second.kills);
//        stmt.addUInt32(x.second.wins);
//        stmt.addUInt32(x.second.lose);
//        stmt.addString(x.second.playername);
//        stmt.addUInt32(x.second.IsRewarded);
//        stmt.Execute();
//    }
//
//    CharacterDatabase.CommitTransaction();
//}

//void PvP::InitialArenaDB(Player* player)
//{
//    auto result = CharacterDatabase.PQuery("SELECT * FROM _玩家竞技场数据 WHERE `guid`=%u", player->GetGUIDLow());
//    if (!result)
//    {
//        CharacterDatabase.PExecute("REPLACE INTO `_玩家竞技场数据` (`guid`, `kills`, `wins`, `lose`, `name`, `rewarded`) VALUES ('%u', '%u', '%u', '%u', '%s','%u')", player->GetGUIDLow(), 0, 0, 0, player->GetName(), 0);
//
//        ArenaTemplate temp;
//        temp.playerguild = player->GetGUIDLow();
//        temp.kills = 0;
//        temp.wins = 0;
//        temp.lose = 0;
//        temp.playername = player->GetName();
//        temp.IsRewarded = 0;
//        ArenaMap.insert(std::make_pair(temp.playerguild, temp));
//    }
//
//    delete result;
//}
//
//void PvP::DaillyRewardResetArenaDB()
//{
//    for (auto& x : ArenaMap)
//        x.second.IsRewarded = 0;
//
//    CharacterDatabase.PExecute(" UPDATE `_玩家竞技场数据` SET `rewarded` = 0 ");
//}
