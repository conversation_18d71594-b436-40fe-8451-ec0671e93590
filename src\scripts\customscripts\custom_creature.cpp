﻿#pragma execution_character_set("utf-8")
#include "scriptPCH.h"
#include "Reward.h"
#include "Switch.h"
#include "stage.h"
#include "Requirement.h"




bool GossipHello_npc_train_all(Player* pPlayer, Creature* pCreature)
{
	pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_TRAINER, "『法术,武器,技能训练』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『新手装备』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF+1);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『专业收益』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF+2);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_INTERACT_2, "『天赋重置』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF+3);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_INTERACT_2, "『免费修理』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF+4);

    if(pPlayer->IsHardcore() && pPlayer->GetLevel()==1)
        pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_INTERACT_2, "『领取我陨落硬核勇士的点券遗产』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 5);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_INTERACT_2, "『武器技能满』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 6);

	pPlayer->SEND_GOSSIP_MENU(pPlayer->GetGossipTextId(pCreature), pCreature->GetGUID());
	return true;
}

bool GossipSelect_npc_train_all(Player* pPlayer, Creature* pCreature, uint32 /*uiSender*/, uint32 uiAction)
{
    if (!pCreature)
        return true;

    if (!pPlayer)
        return true;

    pPlayer->PlayerTalkClass->ClearMenus();

	//法术技能训练
	if (uiAction == GOSSIP_ACTION_INFO_DEF)
	{
		pPlayer->GetSession()->SendTrainerList(pCreature->GetObjectGuid());
	}
    //新手装备
    if (uiAction == GOSSIP_ACTION_INFO_DEF+1)
    {
        pPlayer->GetSession()->SendVendorCustom(30011, pPlayer->GetObjectGuid());
    }

    //专业收益
    if (uiAction == GOSSIP_ACTION_INFO_DEF+2)
    {
        pPlayer->GetSession()->SendListInventory(pCreature->GetObjectGuid());
    }
    //重置天赋
    if (uiAction == GOSSIP_ACTION_INFO_DEF + 3)
    {
        pPlayer->ResetTalents(true);
        pPlayer->SendChatMessage("|cff1eff00※提示※:|r天赋重置成功!");
        pPlayer->CLOSE_GOSSIP_MENU();
        GossipHello_npc_train_all(pPlayer, pCreature);
    }
    //免费修理
    if (uiAction == GOSSIP_ACTION_INFO_DEF + 4)
    {
        pPlayer->DurabilityRepairAll(false, 0);
        pPlayer->SendChatMessage("|cff1eff00※提示※:|r 修理成功!");
        pPlayer->CLOSE_GOSSIP_MENU();
        GossipHello_npc_train_all(pPlayer, pCreature);
    }

    if (uiAction == GOSSIP_ACTION_INFO_DEF + 5)
    {
        // 获取玩家账号ID和当前角色GUID
        uint32 accountId = pPlayer->GetSession()->GetAccountId();
        uint32 currentGuid = pPlayer->GetGUIDLow();
        
        // 查询账号下硬核死亡角色的点券总和，排除当前角色
        uint32 totalTokens = 0;
        auto result = CharacterDatabase.PQuery(
            "SELECT SUM(点券) as total_tokens FROM _玩家点券领取记录 "
            "WHERE accountid = %u AND 硬核模式 = 2 AND guid != %u", 
            accountId, currentGuid);
        
        if (result)
        {
            Field* fields = result->Fetch();
            totalTokens = fields[0].GetInt32();
            delete result;
        }
        
        // 转移点券给当前玩家
        if (totalTokens > 0)
        {
            sRew->AddJiFenByPlayer(pPlayer, totalTokens);
            
            // 清空记录（只删除硬核死亡角色的记录，排除当前角色）
            CharacterDatabase.PExecute(
                "DELETE FROM _玩家点券领取记录 "
                "WHERE accountid = %u AND 硬核模式 = 2 AND guid != %u", 
                accountId, currentGuid);
            
            pPlayer->SendChatMessage("|cff1eff00※提示※:|r 成功领取硬核角色遗产: %u点券!", totalTokens);
        }
        else
        {
            pPlayer->SendChatMessage("|cff1eff00※提示※:|r 没有可领取的硬核角色遗产!");
        }
        pPlayer->CLOSE_GOSSIP_MENU();
        GossipHello_npc_train_all(pPlayer, pCreature);
    }

    if (uiAction == GOSSIP_ACTION_INFO_DEF + 6)
    {
        pPlayer->UpdateSkillsToMaxSkillsForLevel();
        pPlayer->CLOSE_GOSSIP_MENU();
        GossipHello_npc_train_all(pPlayer, pCreature);
    }
    //pPlayer->CLOSE_GOSSIP_MENU();
    return true;
}


bool GossipHello_Shoping_Mall(Player* pPlayer, Creature* pCreature)
{
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『点券商城』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『金币商城』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 1);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『常用道具商城』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 9);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『泡点商城』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 2);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『特殊兑换』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 3);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『副本重置』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 5);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_VENDOR, "『仙术兑换』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 10);
    pPlayer->ADD_GOSSIP_ITEM_EXTENDED(GOSSIP_ICON_CHAT, "『卡密兑换』", GOSSIP_SENDER_MAIN , GOSSIP_ACTION_INFO_DEF+4, "", true);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, "『查询拥有的点券和金币』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 6);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, "『领取充值的点券和金币』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 7);
    pPlayer->ADD_GOSSIP_ITEM(GOSSIP_ICON_CHAT, "『领取挂机奖励(1小时/次)』", GOSSIP_SENDER_MAIN, GOSSIP_ACTION_INFO_DEF + 8);

    pPlayer->SEND_GOSSIP_MENU(pPlayer->GetGossipTextId(pCreature), pCreature->GetGUID());
    return true;
}

bool GossipSelect_Shoping_Mal(Player* pPlayer, Creature* pCreature, uint32 /*uiSender*/, uint32 uiAction)
{
    if (!pCreature || !pPlayer)
        return true;

    pPlayer->PlayerTalkClass->ClearMenus();

    // 使用switch语句替代多个if条件，提高代码可读性和效率
    switch (uiAction)
    {
        case GOSSIP_ACTION_INFO_DEF:      // 点券商城
            pPlayer->GetSession()->SendVendorCustom(30012, pPlayer->GetObjectGuid());
            break;
        case GOSSIP_ACTION_INFO_DEF+1:    // 金币商城
            pPlayer->GetSession()->SendVendorCustom(30013, pPlayer->GetObjectGuid());
            break;
        case GOSSIP_ACTION_INFO_DEF+2:    // 泡点商城
            pPlayer->GetSession()->SendVendorCustom(30014, pPlayer->GetObjectGuid());
            break;
        case GOSSIP_ACTION_INFO_DEF+3:    // 特殊商城
            pPlayer->GetSession()->SendVendorCustom(30015, pPlayer->GetObjectGuid());
            break;
        case GOSSIP_ACTION_INFO_DEF+5:    // 副本重置
            pPlayer->GetSession()->SendVendorCustom(30018, pPlayer->GetObjectGuid());
            break;
        case GOSSIP_ACTION_INFO_DEF+9:    // 常用道具商城 (修复语法错误)
            pPlayer->GetSession()->SendVendorCustom(30023, pPlayer->GetObjectGuid());
            break;
        case GOSSIP_ACTION_INFO_DEF+10:    // 仙术兑换
            pPlayer->GetSession()->SendVendorCustom(30024, pPlayer->GetObjectGuid());
            break;
        case GOSSIP_ACTION_INFO_DEF+6:    // 查询拥有的点券和金币
            {
                uint32 RecTokens = 0;
                uint32 RecGold = 0;
                auto result = CharacterDatabase.PQuery("SELECT 待领取积分, 待领取金币 FROM _玩家信息额外扩展 WHERE guid = %u", pPlayer->GetGUIDLow());
                if (result)
                {
                    Field* fields = result->Fetch();
                    RecTokens = fields[0].GetInt32();
                    RecGold = fields[1].GetInt32();
                    
                    pPlayer->SendChatMessage("|cff1eff00※提示※:|r 当前你拥有|cff1eff00%d点券|r和|cff1eff00%d金币", pPlayer->pTokens, pPlayer->GetMoney()/GOLD);
                    pPlayer->SendChatMessage("|cff1eff00※提示※:|r 当前你拥有尚未领取|cff1eff00%d点券|r和|cff1eff00%d金币", RecTokens, RecGold);
                    delete result;
                }
            }
            break;
        case GOSSIP_ACTION_INFO_DEF+7:    // 领取充值的点券和金币
            {
                uint32 RecTokens = 0;
                uint32 RecGold = 0;
                auto result = CharacterDatabase.PQuery("SELECT 待领取积分, 待领取金币 FROM _玩家信息额外扩展 WHERE guid = %u", pPlayer->GetGUIDLow());
                if (result)
                {
                    Field* fields = result->Fetch();
                    RecTokens = fields[0].GetInt32();
                    RecGold = fields[1].GetInt32();
                    
                    if (RecTokens > 0 || RecGold > 0)
                    {
                        sRew->AddJiFenByPlayer(pPlayer, RecTokens);
                        pPlayer->ModifyMoney(RecGold * GOLD);
                        CharacterDatabase.PExecute("UPDATE `_玩家信息额外扩展` SET 待领取积分=0, 待领取金币=0 WHERE guid = %u", pPlayer->GetGUIDLow());
                        pPlayer->SendChatMessage("|cff1eff00※提示※:|r 领取赞助的|cff1eff00%d点券|r和|cff1eff00%d金币|r成功！", RecTokens, RecGold);
                    }
                    else
                        pPlayer->SendChatMessage("|cff1eff00※提示※:|r 您没有可领取的点券或者金币！");
                    
                    delete result;
                }
            }
            break;
        case GOSSIP_ACTION_INFO_DEF+8:    // 领取挂机奖励
            {
                // 计算未领取的奖励数量
                int32 totalHours = pPlayer->GetTotalPlayedTime() / 3600;
                int32 unRewardNum = totalHours - pPlayer->onlineRewardedCount;

                if (unRewardNum <= 0)
                {
                    // 计算下次可领取时间
                    uint32 totalMinutes = pPlayer->GetTotalPlayedTime() / 60;
                    uint32 nextHourMinutes = (totalHours + 1) * 60;
                    uint32 minutesLeft = nextHourMinutes - totalMinutes;
                    
                    std::string message = std::to_string(minutesLeft) + "分钟后可领取泡点";
                    pPlayer->GetSession()->SendNotification(message.c_str());
                    ChatHandler(pPlayer->GetSession()).PSendSysMessage(message.c_str());
                }
                else
                {
                    // 更新已领取次数
                    uint32 totalcount = totalHours;
                    pPlayer->onlineRewardedCount = totalcount;
                    CharacterDatabase.DirectPExecute("UPDATE _玩家信息额外扩展 SET 泡点奖励次数 = %u WHERE guid = %u", 
                        totalcount, pPlayer->GetGUIDLow());

                    // 发放奖励
                    uint32 rewardid = atoi(sSwitch->GetFlagByIndex(ST_TIME_REW, 1).c_str());
                    for (int32 i = 0; i < unRewardNum; i++)
                        sRew->Rew(pPlayer, rewardid);
                        
                    // 添加提示信息
                    pPlayer->SendChatMessage("|cff1eff00※提示※:|r 成功领取%d次挂机奖励!", unRewardNum);
                }
            }
            break;
    }

    return true;
}

bool isAlphaNumeric(const std::string& str)
{
    return std::all_of(str.begin(), str.end(),
        [](char c)
        {
            return (c >= 'A' && c <= 'Z') || // 大写字母
                (c >= 'a' && c <= 'z') || // 小写字母
                (c >= '0' && c <= '9'); // 数字
        });
}
bool GossipSelectCode_Shoping_Mal(Player* player, Creature* creature, uint32 sender, uint32 action, char const* code)
{
	player->PlayerTalkClass->ClearMenus();

	if (!code || strlen(code) > 10) // 限制CDK长度
	{
		player->SendChatMessage("|cff1eff00※提示※:|r 无效的卡密!");
		return false;
	}

	// 检查是否只包含英文字母和数字
	if (!isAlphaNumeric(code))
	{
		player->SendChatMessage("|cff1eff00※提示※:|r 卡密只能包含英文字母和数字!");
		return false;
	}

	auto result = WorldDatabase.PQuery("SELECT * FROM `_cdk卡密` WHERE `cdk` = '%s'", code);

	if (result)
	{
		Field* fields = result->Fetch();

        uint32 isPromo = fields[5].GetInt32();
        if (isPromo)
        {
            uint32 questid = 30001;
            if (player->GetQuestStatus(questid) == QUEST_STATUS_COMPLETE)
            {
                player->SendChatMessage("|cff1eff00※提示※:|r 今日您已领取过宣传卡密奖励!");
                return false;
            }
            else
            {
                if (const Quest* pQuest = sObjectMgr.GetQuestTemplate(questid))
                {
                    player->AddQuest(pQuest, nullptr);
                    player->CompleteQuest(questid);
                    player->SendChatMessage("|cff1eff00※提示※:|r 成功领取宣传卡密奖励!");
                }
            }

        }

		if (fields[1].GetInt32() > 0 && fields[2].GetInt32() > 0) // uint32 RecItemID = 0;	//uint32 RecItemCount= 0;
			sRew->RewItem(player, fields[1].GetInt32(), fields[2].GetInt32());

		if (fields[3].GetInt32() == 1) // 金币
			player->ModifyMoney(fields[4].GetInt32() * GOLD);

		if (fields[3].GetInt32() == 2) // 点券
			sRew->AddJiFenByPlayer(player, fields[4].GetInt32());

		// 记录卡密兑换日志
		std::string playerName = player->GetName();
		uint32 itemId = fields[1].GetInt32();
		std::string itemName;
		if (itemId > 0) {
			if (ItemPrototype const* item = sObjectMgr.GetItemPrototype(itemId)) {
				itemName = item->Name1;
			}
		}
		uint32 points = 0;
		uint32 gold = 0;
		if (fields[3].GetInt32() == 2) // 点券
			points = fields[4].GetInt32();
		else if (fields[3].GetInt32() == 1) // 金币
			gold = fields[4].GetInt32();

        CharacterDatabase.PExecute("INSERT INTO `_卡密兑换记录` (`guid`,`玩家名`,`卡密`,`物品ID`,`物品名`,`点券`,`金币`) VALUES (%u,'%s','%s',%u,'%s',%u,%u)",
			player->GetGUIDLow(), playerName.c_str(), code, itemId, itemName.c_str(), points, gold);

		player->SendChatMessage("|cff1eff00※提示※:|r 成功兑换卡密!");
		WorldDatabase.PExecute("DELETE FROM `_cdk卡密` WHERE `cdk` = '%s'", code);
	}

	else
		player->SendChatMessage("|cff1eff00※提示※:|r 查无此卡密!");

	return true;
}


bool OnPaTaGossipHello(Player* player, Creature* creature)
{
    player->PlayerTalkClass->ClearMenus();

    auto maplevelup = player->GetMap()->m_MapChallengeLv;

    // 检查地图内是否有敌对单位（BOSS或调整怪物）
    std::list<Unit*> targets;
    MaNGOS::AnyUnfriendlyUnitInObjectRangeCheck u_check(player, player, 500.0f);
    MaNGOS::UnitListSearcher<MaNGOS::AnyUnfriendlyUnitInObjectRangeCheck> searcher(targets, u_check);
    Cell::VisitAllObjects(player, searcher, 500.0f);

    bool hasActiveBoss = false;

    if (!targets.empty())
    {
        for (auto& unit : targets)
        {
            if (unit->IsAlive() && unit->GetFactionTemplateId() != 35)
            {
                // 检查是否为BOSS（等级63）
                if (unit->GetLevel() == 63)
                {
                    hasActiveBoss = true;
                }
            }
        }
    }

    std::ostringstream oss;
    oss << "=== |cffa335ee『弑神之塔』勇者的试炼！|r===|n";
    
    // 只有在没有挑战中时才显示当前挑战等级
    if (!hasActiveBoss)
    {
        oss << "当前挑战等级：|cFFFFFF00" << maplevelup + 1 << " 级|r，勇士准备好迎接试炼了吗？|n";
    }
    
    oss << "|cFF1BE6B8说明：|r 试炼中若你不幸阵亡，将被|cFFFF0000立刻传出塔外|r，无法继续本次挑战。|n |n";

    // 根据地图状态显示不同信息
    if (hasActiveBoss)
    {
        oss << "|cFFFF0000※警告※|r：弑神之塔的封印力量限制了你，一次只能面对一位BOSS！|n";
    }

    player->ADD_GOSSIP_ITEM(GOSSIP_ICON_BATTLE, oss.str().c_str(), GOSSIP_SENDER_MAIN, 0);
    
    // 只有在没有活跃BOSS时才显示开始试炼选项
    if (!hasActiveBoss)
    {
        player->ADD_GOSSIP_ITEM(GOSSIP_ICON_BATTLE, "无所畏惧——开始试炼！|n", GOSSIP_SENDER_MAIN + 1, 0);
        if(player->IsGameMaster())
            player->ADD_GOSSIP_ITEM_EXTENDED(GOSSIP_ICON_CHAT, "|cffff0000▶ 继续挑战弑神之塔|r|n，在弹窗内输入想要挑战的层数！|n ", GOSSIP_SENDER_MAIN + 2, 0, "", true);
    }

    player->SEND_GOSSIP_MENU(DEFAULT_GOSSIP_MESSAGE, creature->GetGUID());
    return true;
}

bool OnPaTaGossipSelect(Player* player, Creature* creature, uint32 sender, uint32 action)
{
    if (!player)
        return false;

    if (sender == GOSSIP_SENDER_MAIN)
    {
        OnPaTaGossipHello(player, creature);
        return true;
    }

    auto maplevelup = player->GetMap()->m_MapChallengeLv;

    //sLog.outString("maplevelup is %u ", maplevelup);

    if (sender == GOSSIP_SENDER_MAIN + 1)
    {
        std::list<Unit*> targets;
        MaNGOS::AnyUnfriendlyUnitInObjectRangeCheck u_check(player, player, 500.0f);
        MaNGOS::UnitListSearcher<MaNGOS::AnyUnfriendlyUnitInObjectRangeCheck> searcher(targets, u_check);
        Cell::VisitAllObjects(player, searcher, 500.0f);

        if (!targets.empty())
        {
            for (auto& cc : targets)
            {
                if (cc->IsAlive() && cc->GetFactionTemplateId() != 35 && cc->GetLevel() == 63)
                {
                    player->SendChatMessage("|cff1eff00※提示※:|r弑神之塔的封印力量限制了你，一次只能面对一位BOSS！");
                    return false;
                }
            }
		}

        bool foundStage = false;
        uint32 currentlevel = maplevelup + 1; //实际调整层数

        for (auto& x : sStage->StageVec)
        {
            if (x.stageRank == currentlevel)
            {
                foundStage = true;

                if (!sReq->Check(player, x.reqID))
                {
                    player->SendChatMessage("|cff1eff00※提示※:|r勇士，你尚未满足进入本关的试炼条件！");
                    break;
                }

                sReq->Des(player, x.reqID);

                //数据库中summonsCreatureId  _属性调整_生物中的mask一样。
                player->GetMap()->m_MapChallengeLv = currentlevel;

                if (auto summonboss = creature->SummonCreature(x.summonsCreatureId, x.summonsMapX, x.summonsMapY, x.summonsMapZ, 0, TEMPSUMMON_TIMED_COMBAT_OR_DEAD_DESPAWN, 250 * IN_MILLISECONDS))
                {
                    player->SendChatMessage("|cff1eff00※提示※:|r试炼已开启，BOSS正在凝聚力量……");
                    // BOSS台词（进入场景时说）
                    summonboss->MonsterYell("愚蠢的凡人，你们的灵魂将献祭于弑神之塔！", LANG_UNIVERSAL, 0);
                    summonboss->SendSpellGo(summonboss, 24425);
                }
                else
                {
                    player->SendChatMessage("|cff1eff00※提示※:|r召唤失败，塔的力量未能回应，请稍后再试或联系管理员！");
                }
                break; // 找到对应关卡后退出循环
            }
        }
        
        if (!foundStage)
        {
            player->SendChatMessage("|cff1eff00※提示※:|r当前等级没有对应的挑战关卡！");
        }    
        player->CLOSE_GOSSIP_MENU();
        return true;
    }
	
    player->CLOSE_GOSSIP_MENU();
    return true;
}



bool OnPaTaGossipcode_goss_select(Player* player, Creature* creature, uint32 sender, uint32 action, char const* code)
{
    player->PlayerTalkClass->ClearMenus();

    if (!*code)
    {
        player->SendChatMessage("|cff1eff00※提示※:|r请输入挑战层数！");
        OnPaTaGossipHello(player, creature);
        return false;
    }

    uint32 dmjLevel = atoi(code);
    if (dmjLevel == 0)
    {
        player->SendChatMessage("|cff1eff00※提示※:|r请输入有效的数字！");
        OnPaTaGossipHello(player, creature);
        return false;
    }

    if (dmjLevel > sStage->StageVec.size())
    {
        player->SendChatMessage("|cff1eff00※提示※:|r层数超出范围！最大层数：%u", (uint32)sStage->StageVec.size());
        //OnPaTaGossipHello(player, creature);
        return false;
    }

    // 使用与OnPaTaGossipSelect相同的检查方式
    std::list<Unit*> targets;
    MaNGOS::AnyUnfriendlyUnitInObjectRangeCheck u_check(player, player, 500.0f);
    MaNGOS::UnitListSearcher<MaNGOS::AnyUnfriendlyUnitInObjectRangeCheck> searcher(targets, u_check);
    Cell::VisitAllObjects(player, searcher, 500.0f);

    if (!targets.empty())
    {
        for (auto& cc : targets)
        {
            if (cc->IsAlive() && cc->GetFactionTemplateId() != 35 && cc->GetLevel() == 63)
            {
                player->SendChatMessage("|cff1eff00※提示※:|r弑神之塔的封印力量限制了你，一次只能面对一位BOSS！");
                return false;
            }
        }
    }

    bool foundStage = false;

    for (auto& x : sStage->StageVec)
    {
        if (x.stageRank == dmjLevel)
        {
            foundStage = true;

            if (!sReq->Check(player, x.reqID))
            {
                player->SendChatMessage("|cff1eff00※提示※:|r勇士，你尚未满足进入本关的试炼条件！");
                break;
            }

            sReq->Des(player, x.reqID);

            // 更新地图挑战等级
            player->GetMap()->m_MapChallengeLv = dmjLevel;

            if (auto summonboss = creature->SummonCreature(x.summonsCreatureId, x.summonsMapX, x.summonsMapY, x.summonsMapZ, 0, TEMPSUMMON_TIMED_COMBAT_OR_DEAD_DESPAWN, 250 * IN_MILLISECONDS))
            {
                player->SendChatMessage("|cff1eff00※提示※:|r试炼已开启，BOSS正在凝聚力量……");
                // BOSS台词（进入场景时说）
                summonboss->MonsterYell("愚蠢的凡人，你们的灵魂将献祭于弑神之塔！", LANG_UNIVERSAL, 0);
                summonboss->SendSpellGo(summonboss, 24425);
            }
            else
            {
                player->SendChatMessage("|cff1eff00※提示※:|r召唤失败，塔的力量未能回应，请稍后再试或联系管理员！");
            }
            break;
        }
    }

    if (!foundStage)
    {
        player->SendChatMessage("|cff1eff00※提示※:|r当前等级没有对应的挑战关卡！");
    }

    player->CLOSE_GOSSIP_MENU();
    return true;
}


enum
{
    //暗黑征服者技能

    SPELL_CLEAVE = 20684,           // 劈砍
    SPELL_STRIKE = 26613,           // 重击
    SPELL_FURIOUS_ANGER = 16791,    // 狂怒之怒
    SPELL_NIMBLE_REFLEXES = 6264,   // 敏捷反射
    SPELL_PIERCE_ARMOR = 6016,      // 穿甲
    SPELL_DETERRENCE = 19263,       // 威慑

    SPELL_PIERCE_ARMOR_PET = 12097,      // 刺穿护甲 75% 20秒

    SPELL_GHOST_VISUAL = 22650,     // 幽灵视觉效果
    SPELL_TWISTING_NETHER = 23700,  // 扭曲虚空

    // 弑神之塔技能
    SPELL_FLAME_BURN = 35782,       // 弑神之塔·烈焰焚烧
    SPELL_POISON_BURST = 35784,     // 弑神之塔·剧毒爆裂
    SPELL_FROST_STRIKE = 35785,     // 弑神之塔·冰霜打击
    SPELL_SHADOW_SHOCK = 35783,     // 弑神之塔·暗影冲击
    SPELL_ARCANE_SHOCK = 35781,     // 弑神之塔·奥术震荡

    MOB_FORLORN_SPIRIT = 80937,     // 绝望之魂


};

enum EventStates
{
    STATE_ENRAGED = 1
};

// I can't do math so this will suffice.
const int8 quikmafs[4][2] = { {5,0},{-5,0},{0,5},{0,-5} };

constexpr auto AGGRO_TEXT_1 = "凡人不得玷污这片土地！";
constexpr auto AGGRO_TEXT_2 = "你们用肮脏的脚步亵渎了主人的土地！";

constexpr auto SUMMON_TEXT_1 = "起来吧，灵魂们。保卫主人的土地！";
constexpr auto SUMMON_TEXT_2 = "灵魂们，起来，击退这些乌合之众！";

constexpr auto DEATH_TEXT_1 = "主人，我很抱歉...我失败了...";
constexpr auto DEATH_TEXT_2 = "主人...他...不太好...";

constexpr auto LEASH_TEXT_1 = "懦夫！如果你们再回来，我会追杀你们！";
constexpr auto LEASH_TEXT_2 = "离开这里，永远不要再踏入这片土地！";


struct boss_PaTaAI : public ScriptedAI
{
    boss_PaTaAI(Creature* c) : ScriptedAI(c)
    {
        Reset();
    }

    uint32 Cleave_Timer;
    uint32 Summon_Announce_Timer;
    uint32 Reflex_Timer;
    uint32 TowerSpell_Timer;        // 弑神之塔技能计时器

    time_t Last_Pierce_Time;
    uint32 SkeletonSummonTime;

    uint8 Event_State;
    uint8 LastHealthPercentage;
    uint32 Biggest_Hit;

    void SetDefaults()
    {
        me->RemoveAurasDueToSpell(SPELL_FURIOUS_ANGER);
        me->RemoveAurasDueToSpell(SPELL_DETERRENCE);

        Cleave_Timer = 10000;
        Reflex_Timer = 26500;
        TowerSpell_Timer = 15000;   // 15秒间隔
        Last_Pierce_Time = 0;
        SkeletonSummonTime = 22000;
        Summon_Announce_Timer = 20000;

        Event_State = 0;
        LastHealthPercentage = 100;


    }

    void Aggro(Unit* who) override
    {
        m_creature->MonsterYell(urand(0, 1) ? AGGRO_TEXT_1 : AGGRO_TEXT_2);

        m_creature->CastSpell(me, 29061, true); //盾墙

    }

    void DespawnAdds()
    {
        // Despawn Lurking Shadow and Forlorn Spirit NPCs
        std::list<Creature*> lCreature;

        m_creature->GetCreatureListWithEntryInGrid(lCreature, MOB_FORLORN_SPIRIT, 200.0f);
        for (std::list<Creature*>::iterator itr = lCreature.begin(); itr != lCreature.end(); ++itr)
            (*itr)->ForcedDespawn();
    }

    void Reset() override
    {
        SetDefaults();
        DespawnAdds();
    }

    void JustRespawned() override
    {
        SetDefaults();
    }

    void JustReachedHome() override
    {

    }

    void KilledUnit(Unit* victim) override
    {
        if (victim->GetTypeId() != TYPEID_PLAYER)
            return;

        if (urand(0, 1)) // Don't spam on wipe.
            m_creature->MonsterYell("加入下面的那些人吧...");
    }

    void JustDied(Unit* pKiller) override
    {
        m_creature->MonsterYell(urand(0, 1) ? DEATH_TEXT_1 : DEATH_TEXT_2);

        uint32 m_respawn_delay_Timer = urand(48 * HOUR, 64 * HOUR);
        m_creature->SetRespawnDelay(m_respawn_delay_Timer);
        m_creature->SetRespawnTime(m_respawn_delay_Timer);
        m_creature->SaveRespawnTime();

        DespawnAdds();


        if (pKiller->IsPlayer() || (pKiller->IsPet() && pKiller->ToCreature()->GetOwner() && pKiller->ToCreature()->GetOwner()->IsPlayer()))
        {
            Player* pOwner = pKiller->IsPlayer() ? pKiller->ToPlayer() : pKiller->ToCreature()->GetOwner()->ToPlayer();

            auto level = pOwner->GetMap()->m_MapChallengeLv;
            if (level <= sStage->StageVec.size())
            {
                for (auto& x : sStage->StageVec)
                {
                    if (x.stageRank == level)
                        sRew->Rew(pOwner, x.rewardID);
                }
                pOwner->SendChatMessage("|cff1eff00※提示※|r：恭喜你成功晋级弑神之塔！");

                std::stringstream oss;
                oss << "|cff1eff00※系统公告※|r：勇士|cffffffff|Hplayer:" << pOwner->GetName() << "|h[" << pOwner->GetName() << "]|h|r"
                    << "在|cff0070dd弑神之塔|r中"
                    << "成功突破第" << std::to_string(level) << "层！";
                sWorld.SendWorldText(3, oss.str().c_str()); // 3 = LANG_SYSTEMMESSAGE
            }

        }


    }

    void DamageTaken(Unit* done_by, uint32& damage) override
    {
        /*只有当伤害既超过300点，又创造新的伤害记录时，才会释放穿刺技能。这是一个"破纪录触发"机制，而不是简单的300点阈值触发。*/
        //if (damage < 300 || damage < Biggest_Hit)
        //    return;

        //// Don't allow to repeat within a 10 second period.
        //if (difftime(time(nullptr), Last_Pierce_Time) >= 10)
        //{
        //    DoCast(me->GetVictim(), SPELL_PIERCE_ARMOR, true);
        //    Last_Pierce_Time = time(nullptr);
        //    Biggest_Hit = damage;
        //}

        if (!damage)
            return;

        if (auto pl = done_by->GetCharmerOrOwnerPlayerOrPlayerItself())
        {
            //盗贼降低30%
            if (pl->GetClass() == CLASS_ROGUE)
            {
                // originalDamage = damage;
                damage = damage * 7 / 10;
                //sLog.outString("DamageTaken - 盗贼攻击伤害调整: %u -> %u (减少20%%)", originalDamage, damage);
            }

            if (auto pet = done_by->ToPet())
            {
                //uint32 originalDamage = damage;
                damage = damage * 8 / 10;
                //sLog.outString("DamageTaken - 宠物攻击伤害调整: %u -> %u (减少20%%)", originalDamage, damage);
            }
        }
    }


    // Called at any Damage to any victim (before damage apply)
    void DamageDeal(Unit* pDoneTo, uint32& uiDamage) override
    {
        if (!uiDamage)
            return;

        if (auto pl = pDoneTo->GetCharmerOrOwnerPlayerOrPlayerItself())
        {
            if (auto pet = pDoneTo->ToPet())
            {
                //uint32 originalDamage = uiDamage;
                uiDamage = uiDamage * 2;
                //sLog.outString("DamageDeal - 对宠物伤害调整: %u -> %u (增加25%%)", originalDamage, uiDamage);
            }
        }

    }

    void UpdateAI(const uint32 diff) override
    {
        if (!m_creature->SelectHostileTarget() || !m_creature->GetVictim())
            return;

        // 劈砍
        if (Cleave_Timer < diff)
        {
            if (DoCastSpellIfCan(m_creature->GetVictim(), SPELL_CLEAVE) == CAST_OK)
                Cleave_Timer = urand(10000, 16000);
        }
        else
            Cleave_Timer -= diff;

        // 敏捷反射
        if (Reflex_Timer < diff)
        {
            if (DoCastSpellIfCan(m_creature->GetVictim(), SPELL_NIMBLE_REFLEXES) == CAST_OK)
                Reflex_Timer = urand(26500, 30000);

            if (m_creature->GetVictim()->IsPet())
            {
                int32 bp1 = -100;
                m_creature->CastCustomSpell(m_creature->GetVictim(), SPELL_PIERCE_ARMOR_PET, &bp1, nullptr, nullptr, true);
                Reflex_Timer = urand(21000, 25000);
            }

        }
        else
            Reflex_Timer -= diff;

        // 每损失10%血量释放重击
        if (LastHealthPercentage - me->GetHealthPercent() >= 25)
        {
            if (DoCastSpellIfCan(m_creature->GetVictim(), SPELL_STRIKE) == CAST_OK)
                LastHealthPercentage = me->GetHealthPercent();
        }

        // 宣布即将召唤
        //if (Summon_Announce_Timer < diff)
        //{
        //    me->MonsterYell(urand(0, 1) ? SUMMON_TEXT_1 : SUMMON_TEXT_2);
        //    Summon_Announce_Timer = 20000;
        //}
        //else
        //    Summon_Announce_Timer -= diff;

        //// 召唤亡魂骷髅
        //if (SkeletonSummonTime < diff)
        //{
        //    // 在25码内选择一个随机的非坦克目标，如果没有则默认为坦克
        //    Unit* randomTarget = GetRandomNearbyEnemyPlayer(me);
        //    if (!randomTarget)
        //        randomTarget = me->GetVictim();

        //    // 随机50/50概率在BOSS或随机玩家位置召唤
        //    float x, y, z, o;
        //    if (urand(0, 1)) // BOSS位置
        //    {
        //        x = me->GetPositionX();
        //        y = me->GetPositionY();
        //        z = me->GetPositionZ();
        //        o = me->GetOrientation();
        //    }
        //    else // 玩家位置
        //    {
        //        x = randomTarget->GetPositionX();
        //        y = randomTarget->GetPositionY();
        //        z = randomTarget->GetPositionZ();
        //        o = randomTarget->GetOrientation();
        //    }

        //    for (uint8 i = 0; i < 4; ++i)
        //    {
        //        if (Creature* spawn = me->SummonCreature(MOB_FORLORN_SPIRIT,
        //            x + quikmafs[i][0],
        //            y + quikmafs[i][1],
        //            z,
        //            o,
        //            TEMPSUMMON_TIMED_DESPAWN_OUT_OF_COMBAT,
        //            5000 // 脱战消失时间
        //        ))
        //        {

        //            spawn->AddAura(SPELL_GHOST_VISUAL);
        //            spawn->CastSpell(spawn, SPELL_TWISTING_NETHER, true); // 视觉效果

        //            // 传递BOSS仇恨列表，但只给很少的仇恨值来识别所有目标
        //            for (auto t : me->GetThreatManager().getThreatList())
        //            {
        //                if (!t || !t->isOnline())
        //                    continue;

        //                float threatAmount = t->getSourceUnit() == randomTarget ? 5.0f : 0.01f;
        //                spawn->GetThreatManager().addThreatDirectly(t->getTarget(), threatAmount);
        //            }

        //            // 与随机目标进入战斗
        //            spawn->SetInCombatWith(randomTarget);
        //            spawn->AI()->AttackStart(randomTarget);
        //        }
        //    }

        //    SkeletonSummonTime = 22000;
        //}
        //else
        //    SkeletonSummonTime -= diff;



        // 弑神之塔随机技能
        if (TowerSpell_Timer < diff)
        {
            uint32 towerSpells[5] = {
                SPELL_FLAME_BURN,
                SPELL_POISON_BURST,
                SPELL_FROST_STRIKE,
                SPELL_SHADOW_SHOCK,
                SPELL_ARCANE_SHOCK
            };

            uint32 randomSpell = towerSpells[urand(0, 4)];

            me->CastSpell(m_creature->GetVictim(), randomSpell, true);

            TowerSpell_Timer = 10000;

        }
        else
            TowerSpell_Timer -= diff;

        // 狂怒之怒（小狂暴）在15%血量时触发
        if (me->GetHealthPercent() < 15 && !(Event_State & STATE_ENRAGED))
        {
            me->MonsterTextEmote("$n变得愤怒了！", me, true);

            DoCast(me, SPELL_FURIOUS_ANGER, true);
            DoCast(me, SPELL_DETERRENCE, true);
            Event_State |= STATE_ENRAGED;
        }

        DoMeleeAttackIfReady();
    }

    // 尝试找到附近不是当前BOSS仇恨目标的敌对玩家
    Unit* GetRandomNearbyEnemyPlayer(Unit* self, uint8 attempt = 0)
    {
        attempt++;
        if (attempt > 5)
            return nullptr;

        Unit* random = self->SelectRandomUnfriendlyTarget(me->GetVictim(), 35.0f);
        if (!random)
            return nullptr;

        // 递归直到选择到玩家（缺少MaNGOS函数来直接做这个...）
        if (!random->IsPlayer() || !self->CanAttack(random))
            return GetRandomNearbyEnemyPlayer(self, attempt);

        return random;
    }
};

CreatureAI* GetAI_boss_PaTa(Creature* creature)
{
    return new boss_PaTaAI(creature);
}


void AddSC_custom_creature_scripts()
{
    Script* newscript;

    newscript = new Script;
    newscript->Name = "npc_train_all";
    newscript->pGossipHello = &GossipHello_npc_train_all;
    newscript->pGossipSelect = &GossipSelect_npc_train_all;
    newscript->RegisterSelf();

    newscript = new Script;
    newscript->Name = "npc_shopping_mall";
    newscript->pGossipHello = &GossipHello_Shoping_Mall;
    newscript->pGossipSelect = &GossipSelect_Shoping_Mal;
    newscript->pGossipSelectWithCode = &GossipSelectCode_Shoping_Mal;
    newscript->RegisterSelf(false);

    newscript = new Script;
    newscript->Name = "custom_Pata_NPC";
    newscript->pGossipHello = &OnPaTaGossipHello;
    newscript->pGossipSelect = &OnPaTaGossipSelect;
    newscript->pGossipSelectWithCode = &OnPaTaGossipcode_goss_select;
    newscript->RegisterSelf(false);  

    newscript = new Script;
    newscript->Name = "boss_PaTa";
    newscript->GetAI = &GetAI_boss_PaTa;
    newscript->RegisterSelf();

}
